# 🌐 Fuentes Web para Documentación TikZ

## 📋 **Recursos Oficiales TikZ/PGF**

### 🎯 **Documentación Principal**
- **Manual Oficial PGF/TikZ**: https://ctan.org/pkg/pgf
  - Versión actual: 3.1.10 (2023)
  - Documentación completa: ~1200 páginas
  - Ejemplos oficiales incluidos
  - Bibliotecas especializadas documentadas

- **CTAN TikZ Packages**: https://ctan.org/topic/graphics-pgf
  - `pgfplots`: Gráficos científicos y estadísticos
  - `tikz-3dplot`: Diagramas 3D avanzados
  - `tikz-cd`: Diagramas conmutativos
  - `tikz-qtree`: Árboles y estructuras

### 🔧 **Herramientas de Desarrollo**
- **TikZiT**: https://tikzit.github.io/
  - Editor gráfico para diagramas TikZ
  - Exportación directa a código TikZ
  - Ideal para diagramas de grafos

- **KtikZ/QtikZ**: https://github.com/fhackenberger/ktikz
  - Editor con vista previa en tiempo real
  - Multiplataforma (Linux, Windows, macOS)
  - Ideal para desarrollo iterativo

## 📊 **Galerías y Ejemplos**

### 🎨 **TeXample.net**
- **URL**: http://www.texample.net/tikz/
- **Categorías relevantes para ICFES**:
  - Mathematics: http://www.texample.net/tikz/examples/area/mathematics/
  - Statistics: http://www.texample.net/tikz/examples/tag/statistics/
  - Geometry: http://www.texample.net/tikz/examples/tag/geometry/
  - 3D: http://www.texample.net/tikz/examples/tag/3d/

### 📈 **PGFPlots Gallery**
- **URL**: http://pgfplots.sourceforge.net/gallery.html
- **Especialización**: Gráficos científicos y estadísticos
- **Relevancia ICFES**: Alta para pensamiento aleatorio
- **Ejemplos destacados**:
  - Bar charts: http://pgfplots.sourceforge.net/gallery.html#barcharts
  - Statistical plots: http://pgfplots.sourceforge.net/gallery.html#statistics

### 🎓 **TikZ Gallery (Academic)**
- **URL**: https://sites.google.com/site/kochiuyu/Tikz
- **Enfoque**: Diagramas académicos y educativos
- **Categorías**:
  - Geometric diagrams
  - Mathematical illustrations
  - Educational graphics

## 🐙 **Repositorios GitHub**

### 📚 **Colecciones TikZ Matemáticas**
- **tikz-examples**: https://github.com/walmes/Tikz
  - Colección extensa de ejemplos
  - Categorizado por tema matemático
  - Código fuente disponible

- **tikz-math**: https://github.com/pgf-tikz/pgf
  - Repositorio oficial PGF/TikZ
  - Últimas actualizaciones y fixes
  - Issues y discusiones técnicas

- **educational-tikz**: https://github.com/search?q=educational+tikz
  - Múltiples repositorios educativos
  - Ejemplos específicos para enseñanza
  - Diagramas para matemáticas escolares

### 🔬 **Repositorios Especializados**
- **tikz-statistical**: https://github.com/search?q=tikz+statistics
  - Gráficos estadísticos específicos
  - Box plots, histogramas, distribuciones
  - Código adaptable para R-exams

- **tikz-geometry**: https://github.com/search?q=tikz+geometry
  - Construcciones geométricas
  - Diagramas 2D y 3D
  - Transformaciones y proyecciones

## 📖 **Overleaf Templates**

### 🎯 **Templates Educativos**
- **Mathematics Templates**: https://www.overleaf.com/latex/templates/tagged/mathematics
  - Filtrar por "tikz" en búsqueda
  - Templates listos para usar
  - Código fuente visible

- **Statistics Templates**: https://www.overleaf.com/latex/templates/tagged/statistics
  - Gráficos estadísticos con TikZ
  - Ejemplos de visualización de datos
  - Compatible con R-exams workflow

### 📊 **Templates Específicos**
- **Geometric Diagrams**: https://www.overleaf.com/latex/templates/tagged/geometry
- **Function Plots**: https://www.overleaf.com/latex/templates/tagged/plots
- **Educational Graphics**: https://www.overleaf.com/latex/templates/tagged/education

## 🎓 **Recursos Académicos**

### 📚 **Universidades con Recursos TikZ**
- **MIT OpenCourseWare**: https://ocw.mit.edu/
  - Buscar "tikz" en materiales de matemáticas
  - Diagramas de alta calidad académica
  - Licencias abiertas

- **Stanford Mathematics**: https://mathematics.stanford.edu/
  - Recursos de visualización matemática
  - Ejemplos de geometría avanzada
  - Aplicaciones educativas

### 📖 **Papers y Publicaciones**
- **TUGboat Articles**: https://tug.org/TUGboat/
  - Artículos técnicos sobre TikZ
  - Mejores prácticas y técnicas avanzadas
  - Casos de uso educativos

- **arXiv Mathematics Education**: https://arxiv.org/list/math.HO/recent
  - Papers sobre visualización matemática
  - Uso de TikZ en educación
  - Metodologías de enseñanza visual

## 🔍 **Estrategias de Búsqueda**

### 🎯 **Palabras Clave Efectivas**
- **Inglés**: "tikz mathematics education", "pgfplots statistics", "tikz geometry diagrams"
- **Español**: "tikz matemáticas educación", "diagramas geométricos tikz"
- **Específicas ICFES**: "tikz probability", "tikz functions", "tikz 3d geometry"

### 📋 **Filtros de Búsqueda**
- **GitHub**: `language:tex tikz mathematics`
- **Google Scholar**: `"tikz" "mathematics education" filetype:pdf`
- **Overleaf**: Filtrar por tags: mathematics, statistics, geometry

### 🔄 **Búsqueda Recursiva**
1. **Partir de un ejemplo relevante**
2. **Seguir referencias y enlaces relacionados**
3. **Explorar perfiles de autores prolíficos**
4. **Revisar repositorios de colaboradores**
5. **Buscar variaciones y mejoras del código**

## ⚡ **Recursos de Actualización Automática**

### 📡 **RSS/Feeds**
- **CTAN Updates**: https://ctan.org/ctan-ann
- **TikZ GitHub Releases**: https://github.com/pgf-tikz/pgf/releases
- **TeXample.net New Examples**: http://www.texample.net/tikz/examples/recent/

### 🔔 **Notificaciones**
- **GitHub Watch**: Repositorios relevantes
- **Google Alerts**: "new tikz examples mathematics"
- **Reddit**: r/LaTeX, r/mathematics subreddits

---

**Última actualización**: `r Sys.Date()`  
**Próxima revisión**: Trimestral  
**Mantenedor**: Proyecto ICFES R-exams